<?php

namespace App\Console\Commands;

use App\Services\GoPlusService;
use Illuminate\Console\Command;

class TestGoPlusConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'goplus:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test GoPlus API connection and authentication';

    /**
     * Execute the console command.
     */
    public function handle(GoPlusService $goPlusService): int
    {
        $this->info('Testing GoPlus API connection...');
        $this->newLine();

        try {
            $result = $goPlusService->testConnection();

            if ($result['status'] === 'success') {
                $this->info('✅ ' . $result['message']);
                $this->info('✅ Access token obtained: ' . ($result['token_obtained'] ? 'Yes' : 'No'));
                $this->info('✅ API responsive: ' . ($result['api_responsive'] ? 'Yes' : 'No'));
                return Command::SUCCESS;
            } else {
                $this->error('❌ ' . $result['message']);
                $this->error('❌ Access token obtained: ' . ($result['token_obtained'] ? 'Yes' : 'No'));
                $this->error('❌ API responsive: ' . ($result['api_responsive'] ? 'Yes' : 'No'));
                
                $this->newLine();
                $this->warn('Please check your GoPlus API credentials in your .env file:');
                $this->warn('- GOPLUS_API_KEY');
                $this->warn('- GOPLUS_API_SECRET');
                $this->newLine();
                $this->warn('You can obtain these credentials from: https://gopluslabs.io/security-api');
                
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error('❌ Failed to test GoPlus connection: ' . $e->getMessage());
            
            $this->newLine();
            $this->warn('Please check your GoPlus API credentials in your .env file:');
            $this->warn('- GOPLUS_API_KEY');
            $this->warn('- GOPLUS_API_SECRET');
            $this->newLine();
            $this->warn('You can obtain these credentials from: https://gopluslabs.io/security-api');
            
            return Command::FAILURE;
        }
    }
}
