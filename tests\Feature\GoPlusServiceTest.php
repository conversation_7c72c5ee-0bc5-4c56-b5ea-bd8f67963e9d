<?php

namespace Tests\Feature;

use App\Services\GoPlusService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class GoPlusServiceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear any cached tokens
        Cache::forget('goplus_access_token');
    }

    public function test_service_detects_missing_credentials()
    {
        // Clear environment variables
        config(['services.goplus.api_key' => null]);
        config(['services.goplus.api_secret' => null]);
        
        $service = new GoPlusService();
        
        $this->assertFalse($service->hasCredentials());
    }

    public function test_service_detects_present_credentials()
    {
        // Set test credentials
        config(['services.goplus.api_key' => 'test_key']);
        config(['services.goplus.api_secret' => 'test_secret']);
        
        $service = new GoPlusService();
        
        $this->assertTrue($service->hasCredentials());
    }

    public function test_access_token_request_with_valid_credentials()
    {
        // Set test credentials
        config(['services.goplus.api_key' => 'test_key']);
        config(['services.goplus.api_secret' => 'test_secret']);

        // Mock the token request
        Http::fake([
            'api.gopluslabs.io/api/v1/token' => Http::response([
                'result' => [
                    'access_token' => 'test_access_token_123'
                ]
            ], 200)
        ]);

        $service = new GoPlusService();
        $result = $service->testConnection();

        $this->assertEquals('success', $result['status']);
        $this->assertTrue($result['token_obtained']);
    }

    public function test_access_token_request_with_invalid_credentials()
    {
        // Set test credentials
        config(['services.goplus.api_key' => 'invalid_key']);
        config(['services.goplus.api_secret' => 'invalid_secret']);

        // Mock failed token request
        Http::fake([
            'api.gopluslabs.io/api/v1/token' => Http::response([
                'message' => 'Invalid credentials'
            ], 401)
        ]);

        $service = new GoPlusService();
        $result = $service->testConnection();

        $this->assertEquals('error', $result['status']);
        $this->assertFalse($result['token_obtained']);
    }

    public function test_token_security_analysis_with_mocked_response()
    {
        // Set test credentials
        config(['services.goplus.api_key' => 'test_key']);
        config(['services.goplus.api_secret' => 'test_secret']);

        // Mock both token and security analysis requests
        Http::fake([
            'api.gopluslabs.io/api/v1/token' => Http::response([
                'result' => [
                    'access_token' => 'test_access_token_123'
                ]
            ], 200),
            'api.gopluslabs.io/api/v1/token_security/*' => Http::response([
                'result' => [
                    '0x1234567890123456789012345678901234567890' => [
                        'is_honeypot' => '0',
                        'is_open_source' => '1',
                        'is_mintable' => '0',
                        'buy_tax' => '0.05',
                        'sell_tax' => '0.05',
                        'holder_count' => '1000',
                        'total_supply' => '1000000000'
                    ]
                ]
            ], 200)
        ]);

        $service = new GoPlusService();
        $result = $service->analyzeTokenSecurity('0x1234567890123456789012345678901234567890', '1');

        $this->assertIsArray($result);
        $this->assertArrayHasKey('score', $result);
        $this->assertArrayHasKey('is_honeypot', $result);
        $this->assertArrayHasKey('is_open_source', $result);
        $this->assertFalse($result['is_honeypot']);
        $this->assertTrue($result['is_open_source']);
    }
}
